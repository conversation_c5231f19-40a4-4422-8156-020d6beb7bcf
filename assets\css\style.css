/* EventLynx Custom Styles - Modern Color Scheme */

:root {
    /* Primary Brand Colors */
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #a5b4fc;
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    /* Secondary Colors */
    --secondary-color: #64748b;
    --secondary-light: #94a3b8;
    --secondary-dark: #475569;

    /* Accent Colors */
    --accent-orange: #f97316;
    --accent-pink: #ec4899;
    --accent-green: #10b981;
    --accent-blue: #3b82f6;
    --accent-purple: #8b5cf6;

    /* Status Colors */
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #06b6d4;

    /* Neutral Colors */
    --light-color: #f8fafc;
    --lighter-color: #ffffff;
    --dark-color: #1e293b;
    --darker-color: #0f172a;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* Typography */
    --font-primary: 'Poppins', sans-serif;
    --font-secondary: 'Inter', sans-serif;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;
}

/* Global Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-secondary);
    line-height: 1.6;
    color: var(--gray-700);
    background-color: var(--gray-50);
    font-weight: 400;
}

h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-primary);
    font-weight: 600;
    color: var(--gray-800);
    line-height: 1.3;
}

.text-primary {
    color: var(--primary-color) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.hero-section {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 80px 0;
    text-align: center;
}

.hero-section h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.hero-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

/* Event Cards - Updated */
.event-card {
    border: none;
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    overflow: hidden;
    height: 100%;
    background: white;
    border: 1px solid var(--gray-100);
    position: relative;
}

.event-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.event-card:hover::before {
    transform: scaleX(1);
}

.event-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-light);
}

.event-card img {
    height: 200px;
    object-fit: cover;
    width: 100%;
}

.event-card .card-body {
    padding: 2rem;
    position: relative;
    z-index: 2;
}

.event-card .event-date {
    background: var(--primary-gradient);
    color: white;
    padding: 0.75rem 1.25rem;
    border-radius: var(--radius-full);
    font-size: 0.875rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
    font-family: var(--font-primary);
}

.event-card .event-price {
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--accent-green);
    font-family: var(--font-primary);
}

.event-card .event-location {
    color: var(--gray-500);
    font-size: 0.95rem;
    font-weight: 500;
}

.event-card .card-title {
    font-family: var(--font-primary);
    font-weight: 700;
    color: var(--gray-800);
    font-size: 1.25rem;
    line-height: 1.3;
}

.event-card .card-text {
    color: var(--gray-600);
    line-height: 1.6;
}

/* Search Form */
.search-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-top: -50px;
    position: relative;
    z-index: 10;
}

/* Cart Styles */
.cart-item {
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    background: white;
}

.cart-summary {
    background: var(--light-color);
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

/* Dashboard Styles */
.dashboard-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
}

.stat-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
}

.stat-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* Admin Panel Styles */
.admin-sidebar {
    background: var(--dark-color);
    min-height: calc(100vh - 76px);
    padding: 2rem 0;
}

.admin-sidebar .nav-link {
    color: #adb5bd;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    margin: 0.25rem 1rem;
    transition: all 0.3s ease;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    background: var(--primary-color);
    color: white;
}

.admin-content {
    padding: 2rem;
}

/* Form Styles */
.form-floating > label {
    color: var(--secondary-color);
}

.btn-primary {
    background: var(--primary-color);
    border: none;
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

/* Booking History */
.booking-card {
    border-left: 4px solid var(--primary-color);
    background: white;
    border-radius: 0 10px 10px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.booking-status {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.status-completed {
    background: var(--success-color);
    color: white;
}

.status-pending {
    background: var(--warning-color);
    color: var(--dark-color);
}

.status-failed {
    background: var(--danger-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-section p {
        font-size: 1rem;
    }
    
    .search-form {
        margin-top: -30px;
        padding: 1.5rem;
    }
    
    .admin-sidebar {
        min-height: auto;
    }
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.bg-primary-custom {
    background-color: var(--primary-color) !important;
}

.border-primary-custom {
    border-color: var(--primary-color) !important;
}

/* Modern Navbar Styles */
.navbar-large {
    padding: 1rem 0;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    background: var(--primary-gradient) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.brand-container {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.brand-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
    line-height: 1;
    margin: 0;
}

.brand-tagline {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 400;
    margin: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.nav-link-modern {
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: var(--radius-lg);
    transition: all 0.3s ease;
    color: rgba(255, 255, 255, 0.9) !important;
    position: relative;
}

.nav-link-modern:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white !important;
    transform: translateY(-1px);
}

.nav-link-modern i {
    font-size: 1rem;
}

.cart-link {
    position: relative;
}

.cart-badge {
    position: absolute;
    top: 0.25rem;
    right: 0.25rem;
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    border-radius: var(--radius-full);
    background: var(--warning-color) !important;
    color: var(--dark-color) !important;
    font-weight: 600;
    min-width: 1.5rem;
    text-align: center;
}

.user-dropdown {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem !important;
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.user-dropdown:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.user-avatar {
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.user-name {
    font-weight: 500;
    color: white;
}

.dropdown-menu-modern {
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: 0.5rem;
    margin-top: 0.5rem;
    background: white;
    backdrop-filter: blur(10px);
    min-width: 250px;
}

.dropdown-header .user-info {
    padding: 0.75rem;
    text-align: center;
}

.dropdown-item-modern {
    padding: 0.75rem 1rem;
    border-radius: var(--radius-lg);
    margin: 0.125rem 0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    font-weight: 500;
    color: var(--gray-700);
}

.dropdown-item-modern:hover {
    background: var(--gray-100);
    color: var(--primary-color);
    transform: translateX(5px);
}

.dropdown-item-modern i {
    width: 20px;
    text-align: center;
}

.admin-link:hover {
    background: rgba(var(--warning-color), 0.1);
    color: var(--warning-color);
}

.logout-link:hover {
    background: rgba(var(--danger-color), 0.1);
    color: var(--danger-color);
}

.btn-modern {
    border-radius: var(--radius-full);
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-modern:hover {
    background: white;
    color: var(--primary-color) !important;
    border-color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.bg-gradient-primary {
    background: var(--primary-gradient) !important;
}

.bg-gradient-warning {
    background: linear-gradient(45deg, var(--warning-color), var(--accent-orange)) !important;
}

/* Updated Hero Section with New Colors */
.hero-section-new {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--accent-purple) 50%, var(--accent-pink) 100%);
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.text-gradient {
    background: linear-gradient(45deg, var(--accent-orange), var(--accent-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-white-75 {
    color: rgba(255, 255, 255, 0.9);
}

.btn-primary-custom {
    background: linear-gradient(45deg, var(--accent-blue), var(--primary-color));
    border: none;
    border-radius: var(--radius-full);
    padding: 1rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-primary-custom:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
    background: linear-gradient(45deg, var(--primary-dark), var(--accent-blue));
}

.hero-image-container {
    position: relative;
    z-index: 2;
}

.floating-card {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.event-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    background: var(--primary-gradient);
    color: white;
    border-radius: var(--radius-xl);
}

.min-vh-75 {
    min-height: 75vh;
}

/* Updated Search Section */
.search-section {
    margin-top: -80px;
    position: relative;
    z-index: 10;
}

.search-card {
    border-radius: var(--radius-2xl);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-xl);
}

.search-input-group {
    position: relative;
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
    z-index: 5;
}

.search-input {
    padding-left: 45px;
    border-radius: var(--radius-xl);
    border: 2px solid var(--gray-200);
    transition: all 0.3s ease;
    background: white;
}

.search-input:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
    background: white;
}

.search-btn {
    border-radius: var(--radius-xl);
    font-weight: 600;
    background: var(--primary-gradient);
    border: none;
    color: white;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Updated Statistics Section */
.stats-section {
    background: linear-gradient(135deg, var(--gray-50) 0%, white 100%);
}

.stat-card-new {
    background: white;
    border-radius: var(--radius-2xl);
    padding: 2.5rem 2rem;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
    border: 1px solid var(--gray-100);
    position: relative;
    overflow: hidden;
}

.stat-card-new::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.stat-card-new:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-light);
}

.stat-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    position: relative;
}

.stat-icon::after {
    content: '';
    position: absolute;
    inset: -2px;
    border-radius: var(--radius-full);
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    z-index: -1;
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 800;
    color: var(--gray-800);
    margin: 1rem 0;
    font-family: var(--font-primary);
}

.stat-label {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--gray-700);
    margin-bottom: 0.5rem;
    font-family: var(--font-primary);
}

/* Updated Feature Cards */
.features-section {
    background: white;
}

.feature-card {
    background: white;
    border-radius: var(--radius-2xl);
    padding: 3rem 2rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border: 1px solid var(--gray-100);
    height: 100%;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-light);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2rem;
    color: white;
    position: relative;
}

.feature-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--gray-800);
    margin: 1.5rem 0 1rem;
    font-family: var(--font-primary);
}

.feature-description {
    color: var(--gray-600);
    line-height: 1.7;
    margin-bottom: 2rem;
    font-size: 1rem;
}

.feature-badge {
    margin-top: auto;
}

.feature-badge .badge {
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: 0.8rem;
}
