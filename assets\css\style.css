/* EventLynx Custom Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #0dcaf0;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
}

.hero-section {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    padding: 80px 0;
    text-align: center;
}

.hero-section h1 {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.hero-section p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

/* Event Cards */
.event-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    overflow: hidden;
    height: 100%;
}

.event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.event-card img {
    height: 200px;
    object-fit: cover;
    width: 100%;
}

.event-card .card-body {
    padding: 1.5rem;
}

.event-card .event-date {
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 1rem;
}

.event-card .event-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--success-color);
}

.event-card .event-location {
    color: var(--secondary-color);
    font-size: 0.9rem;
}

/* Search Form */
.search-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    margin-top: -50px;
    position: relative;
    z-index: 10;
}

/* Cart Styles */
.cart-item {
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1rem;
    background: white;
}

.cart-summary {
    background: var(--light-color);
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

/* Dashboard Styles */
.dashboard-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
}

.stat-card {
    background: linear-gradient(135deg, var(--primary-color), #0056b3);
    color: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
}

.stat-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* Admin Panel Styles */
.admin-sidebar {
    background: var(--dark-color);
    min-height: calc(100vh - 76px);
    padding: 2rem 0;
}

.admin-sidebar .nav-link {
    color: #adb5bd;
    padding: 0.75rem 1.5rem;
    border-radius: 5px;
    margin: 0.25rem 1rem;
    transition: all 0.3s ease;
}

.admin-sidebar .nav-link:hover,
.admin-sidebar .nav-link.active {
    background: var(--primary-color);
    color: white;
}

.admin-content {
    padding: 2rem;
}

/* Form Styles */
.form-floating > label {
    color: var(--secondary-color);
}

.btn-primary {
    background: var(--primary-color);
    border: none;
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #0056b3;
    transform: translateY(-1px);
}

/* Booking History */
.booking-card {
    border-left: 4px solid var(--primary-color);
    background: white;
    border-radius: 0 10px 10px 0;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.booking-status {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.status-completed {
    background: var(--success-color);
    color: white;
}

.status-pending {
    background: var(--warning-color);
    color: var(--dark-color);
}

.status-failed {
    background: var(--danger-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section h1 {
        font-size: 2.5rem;
    }
    
    .hero-section p {
        font-size: 1rem;
    }
    
    .search-form {
        margin-top: -30px;
        padding: 1.5rem;
    }
    
    .admin-sidebar {
        min-height: auto;
    }
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Utility Classes */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.bg-primary-custom {
    background-color: var(--primary-color) !important;
}

.border-primary-custom {
    border-color: var(--primary-color) !important;
}
