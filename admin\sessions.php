<?php
require_once '../config/config.php';

// Require admin login
requireAdmin();

$page_title = 'Session Management';

// Get session statistics
$stats = getSessionStats();

// Get recent sessions
$db = getDB();
$stmt = $db->query("
    SELECT s.*, u.first_name, u.last_name, u.email 
    FROM user_sessions s 
    LEFT JOIN users u ON s.user_id = u.id 
    WHERE s.is_active = 1 AND s.expires_at > NOW()
    ORDER BY s.last_activity DESC 
    LIMIT 50
");
$recent_sessions = $stmt->fetchAll();

include '../includes/header.php';
include 'includes/admin_sidebar.php';
?>

<div class="admin-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-shield-alt me-2"></i>Session Management</h2>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stat-icon bg-primary-gradient mb-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="stat-number"><?php echo number_format($stats['total_active']); ?></h3>
                    <p class="stat-label">Active Sessions</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stat-icon bg-success-gradient mb-3">
                        <i class="fas fa-user-check"></i>
                    </div>
                    <h3 class="stat-number"><?php echo number_format($stats['unique_users']); ?></h3>
                    <p class="stat-label">Unique Users</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stat-icon bg-warning-gradient mb-3">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="stat-number"><?php echo number_format($stats['active_5min']); ?></h3>
                    <p class="stat-label">Active (5 min)</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="stat-icon bg-info-gradient mb-3">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <h3 class="stat-number">
                        <?php echo $stats['total_active'] > 0 ? round(($stats['active_5min'] / $stats['total_active']) * 100) : 0; ?>%
                    </h3>
                    <p class="stat-label">Activity Rate</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Sessions -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>Recent Active Sessions
            </h5>
        </div>
        <div class="card-body p-0">
            <?php if (empty($recent_sessions)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Active Sessions</h5>
                    <p class="text-muted">No users are currently logged in.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>User</th>
                                <th>Device</th>
                                <th>Location</th>
                                <th>IP Address</th>
                                <th>Last Activity</th>
                                <th>Duration</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($recent_sessions as $session): ?>
                                <?php 
                                $device_info = json_decode($session['device_info'], true);
                                $location_info = json_decode($session['location_info'], true);
                                $last_activity = strtotime($session['last_activity']);
                                $created_at = strtotime($session['created_at']);
                                $duration = time() - $created_at;
                                $minutes_ago = (time() - $last_activity) / 60;
                                ?>
                                <tr>
                                    <td>
                                        <?php if ($session['user_id']): ?>
                                            <div>
                                                <div class="fw-bold">
                                                    <?php echo htmlspecialchars($session['first_name'] . ' ' . $session['last_name']); ?>
                                                </div>
                                                <small class="text-muted">
                                                    <?php echo htmlspecialchars($session['email']); ?>
                                                </small>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">Guest Session</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="me-2">
                                                <?php if ($device_info['device'] === 'Mobile'): ?>
                                                    <i class="fas fa-mobile-alt text-primary"></i>
                                                <?php elseif ($device_info['device'] === 'Tablet'): ?>
                                                    <i class="fas fa-tablet-alt text-info"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-desktop text-secondary"></i>
                                                <?php endif; ?>
                                            </div>
                                            <div>
                                                <div class="small fw-bold">
                                                    <?php echo htmlspecialchars($device_info['browser'] ?? 'Unknown'); ?>
                                                </div>
                                                <div class="small text-muted">
                                                    <?php echo htmlspecialchars($device_info['os'] ?? 'Unknown'); ?>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="small">
                                            <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                            <?php echo htmlspecialchars($location_info['city'] ?? 'Unknown'); ?>
                                        </div>
                                        <div class="small text-muted">
                                            <?php echo htmlspecialchars($location_info['country'] ?? 'Unknown'); ?>
                                        </div>
                                    </td>
                                    <td>
                                        <code class="small"><?php echo htmlspecialchars($session['ip_address']); ?></code>
                                    </td>
                                    <td>
                                        <div class="small">
                                            <?php echo date('M j, g:i A', $last_activity); ?>
                                        </div>
                                        <div class="small text-muted">
                                            <?php 
                                            if ($minutes_ago < 1) {
                                                echo "Just now";
                                            } elseif ($minutes_ago < 60) {
                                                echo round($minutes_ago) . " min ago";
                                            } else {
                                                echo round($minutes_ago / 60) . " hr ago";
                                            }
                                            ?>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="small">
                                            <?php 
                                            $hours = floor($duration / 3600);
                                            $minutes = floor(($duration % 3600) / 60);
                                            
                                            if ($hours > 0) {
                                                echo $hours . "h " . $minutes . "m";
                                            } else {
                                                echo $minutes . " min";
                                            }
                                            ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($minutes_ago < 5): ?>
                                            <span class="badge bg-success">
                                                <i class="fas fa-circle me-1"></i>Active
                                            </span>
                                        <?php elseif ($minutes_ago < 30): ?>
                                            <span class="badge bg-warning">
                                                <i class="fas fa-clock me-1"></i>Recent
                                            </span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">
                                                <i class="fas fa-pause me-1"></i>Idle
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Session Management Info -->
    <div class="row mt-4">
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-database fa-2x text-primary mb-3"></i>
                    <h5>Database Sessions</h5>
                    <p class="text-muted small">Sessions are stored in the database for better scalability and control.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-clock fa-2x text-warning mb-3"></i>
                    <h5>Auto Cleanup</h5>
                    <p class="text-muted small">Expired sessions are automatically cleaned up by the garbage collector.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="fas fa-shield-alt fa-2x text-success mb-3"></i>
                    <h5>Security</h5>
                    <p class="text-muted small">Device fingerprinting and IP tracking for enhanced security.</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 1.5rem;
    color: white;
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: var(--gray-800);
    margin: 0.5rem 0;
    font-family: var(--font-primary);
}

.stat-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--gray-600);
    margin: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.875rem;
}

.table td {
    vertical-align: middle;
}
</style>

<?php include '../includes/footer.php'; ?>
