<nav class="navbar navbar-expand-lg navbar-dark bg-gradient-primary sticky-top navbar-large">
    <div class="container-fluid px-4">
        <a class="navbar-brand fw-bold brand-logo" href="<?php echo SITE_URL; ?>">
            <div class="brand-container">
                <div class="brand-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <div class="brand-text">
                    <span class="brand-name">EventLynx</span>
                    <small class="brand-tagline">Event Booking Platform</small>
                </div>
            </div>
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link nav-link-modern" href="<?php echo SITE_URL; ?>">
                        <i class="fas fa-home me-2"></i>Home
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link nav-link-modern" href="<?php echo SITE_URL; ?>/events/search.php">
                        <i class="fas fa-search me-2"></i>Browse Events
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link nav-link-modern" href="<?php echo SITE_URL; ?>/events/search.php?category=Technology">
                        <i class="fas fa-laptop me-2"></i>Tech Events
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link nav-link-modern" href="<?php echo SITE_URL; ?>/events/search.php?category=Music">
                        <i class="fas fa-music me-2"></i>Music
                    </a>
                </li>
            </ul>
            
            <ul class="navbar-nav">
                <?php if (isLoggedIn()): ?>
                    <li class="nav-item">
                        <a class="nav-link nav-link-modern cart-link" href="<?php echo SITE_URL; ?>/cart/cart.php">
                            <i class="fas fa-shopping-cart me-2"></i>Cart
                            <?php
                            // Get cart count
                            $db = getDB();
                            $cart_count = 0;
                            if (isset($_SESSION['user_id'])) {
                                $stmt = $db->prepare("SELECT SUM(quantity) as total FROM cart WHERE user_id = ?");
                                $stmt->execute([$_SESSION['user_id']]);
                                $result = $stmt->fetch();
                                $cart_count = $result['total'] ?? 0;
                            } elseif (isset($_SESSION['session_id'])) {
                                $stmt = $db->prepare("SELECT SUM(quantity) as total FROM cart WHERE session_id = ?");
                                $stmt->execute([$_SESSION['session_id']]);
                                $result = $stmt->fetch();
                                $cart_count = $result['total'] ?? 0;
                            }
                            if ($cart_count > 0): ?>
                                <span class="badge bg-gradient-warning text-dark cart-badge"><?php echo $cart_count; ?></span>
                            <?php endif; ?>
                        </a>
                    </li>
                    
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle user-dropdown" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                            </div>
                            <span class="user-name"><?php echo $_SESSION['first_name']; ?></span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-modern">
                            <li class="dropdown-header">
                                <div class="user-info">
                                    <strong><?php echo $_SESSION['first_name'] . ' ' . $_SESSION['last_name']; ?></strong>
                                    <small class="text-muted"><?php echo $_SESSION['email']; ?></small>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item dropdown-item-modern" href="<?php echo SITE_URL; ?>/user/dashboard.php">
                                <i class="fas fa-tachometer-alt me-3"></i>Dashboard
                            </a></li>
                            <li><a class="dropdown-item dropdown-item-modern" href="<?php echo SITE_URL; ?>/user/bookings.php">
                                <i class="fas fa-ticket-alt me-3"></i>My Bookings
                            </a></li>
                            <li><a class="dropdown-item dropdown-item-modern" href="<?php echo SITE_URL; ?>/auth/profile.php">
                                <i class="fas fa-user-edit me-3"></i>Profile Settings
                            </a></li>
                            <?php if (isAdmin()): ?>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item dropdown-item-modern admin-link" href="<?php echo SITE_URL; ?>/admin/">
                                    <i class="fas fa-cog me-3"></i>Admin Panel
                                </a></li>
                            <?php endif; ?>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item dropdown-item-modern logout-link" href="<?php echo SITE_URL; ?>/auth/logout.php">
                                <i class="fas fa-sign-out-alt me-3"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                <?php else: ?>
                    <li class="nav-item">
                        <a class="nav-link nav-link-modern" href="<?php echo SITE_URL; ?>/auth/login.php">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-outline-light btn-modern ms-2" href="<?php echo SITE_URL; ?>/auth/register.php">
                            <i class="fas fa-user-plus me-2"></i>Join Now
                        </a>
                    </li>
                <?php endif; ?>
            </ul>
        </div>
    </div>
</nav>
