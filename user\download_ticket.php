<?php
require_once '../config/config.php';
requireLogin();

$booking_reference = $_GET['booking'] ?? '';
$user_id = $_SESSION['user_id'];

if (empty($booking_reference)) {
    header('Location: bookings.php');
    exit();
}

// Get booking details
$db = getDB();
$stmt = $db->prepare("
    SELECT b.*, e.title, e.event_date, e.event_time, e.venue, e.address, e.city, e.organizer_name
    FROM bookings b 
    JOIN events e ON b.event_id = e.id 
    WHERE b.booking_reference = ? AND b.user_id = ? AND b.payment_status = 'completed'
");
$stmt->execute([$booking_reference, $user_id]);
$booking = $stmt->fetch();

if (!$booking) {
    header('Location: bookings.php');
    exit();
}

// Set headers for PDF download (in real implementation, use a PDF library like TCPDF or FPDF)
header('Content-Type: text/html');
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Event Ticket - <?php echo htmlspecialchars($booking['title']); ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @media print {
            .no-print { display: none !important; }
            .ticket { page-break-inside: avoid; }
        }
        
        .ticket {
            border: 2px dashed #007bff;
            border-radius: 15px;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            position: relative;
            overflow: hidden;
        }
        
        .ticket::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #007bff, #0056b3);
        }
        
        .qr-placeholder {
            width: 120px;
            height: 120px;
            background: #f8f9fa;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <!-- Print Controls -->
        <div class="no-print mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="fas fa-ticket-alt me-2"></i>Event Ticket</h2>
                <div>
                    <button onclick="window.print()" class="btn btn-primary me-2">
                        <i class="fas fa-print me-1"></i>Print Ticket
                    </button>
                    <a href="bookings.php" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Bookings
                    </a>
                </div>
            </div>
        </div>

        <!-- Ticket -->
        <div class="ticket p-4 mb-4">
            <div class="row">
                <!-- Event Details -->
                <div class="col-md-8">
                    <div class="mb-3">
                        <h1 class="h3 text-primary mb-2"><?php echo htmlspecialchars($booking['title']); ?></h1>
                        <p class="text-muted mb-0">Organized by <?php echo htmlspecialchars($booking['organizer_name']); ?></p>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-sm-6">
                            <div class="mb-3">
                                <h6 class="text-uppercase text-muted mb-1">Date & Time</h6>
                                <p class="mb-0">
                                    <i class="fas fa-calendar text-primary me-2"></i>
                                    <?php echo formatDate($booking['event_date']); ?>
                                </p>
                                <p class="mb-0">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    <?php echo formatTime($booking['event_time']); ?>
                                </p>
                            </div>
                        </div>
                        
                        <div class="col-sm-6">
                            <div class="mb-3">
                                <h6 class="text-uppercase text-muted mb-1">Venue</h6>
                                <p class="mb-0">
                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                    <?php echo htmlspecialchars($booking['venue']); ?>
                                </p>
                                <p class="mb-0 text-muted">
                                    <?php echo htmlspecialchars($booking['address'] . ', ' . $booking['city']); ?>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="mb-3">
                                <h6 class="text-uppercase text-muted mb-1">Attendee</h6>
                                <p class="mb-0">
                                    <i class="fas fa-user text-primary me-2"></i>
                                    <?php echo htmlspecialchars($booking['attendee_name']); ?>
                                </p>
                                <p class="mb-0 text-muted">
                                    <?php echo htmlspecialchars($booking['attendee_email']); ?>
                                </p>
                            </div>
                        </div>
                        
                        <div class="col-sm-6">
                            <div class="mb-3">
                                <h6 class="text-uppercase text-muted mb-1">Ticket Details</h6>
                                <p class="mb-0">
                                    <i class="fas fa-ticket-alt text-primary me-2"></i>
                                    Quantity: <?php echo $booking['quantity']; ?>
                                </p>
                                <p class="mb-0">
                                    <i class="fas fa-dollar-sign text-primary me-2"></i>
                                    Total: <?php echo formatCurrency($booking['total_amount']); ?>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- QR Code and Booking Reference -->
                <div class="col-md-4 text-center">
                    <div class="qr-placeholder mx-auto mb-3">
                        <div>
                            <i class="fas fa-qrcode fa-3x text-muted"></i>
                            <p class="small text-muted mt-2 mb-0">QR Code</p>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-uppercase text-muted mb-1">Booking Reference</h6>
                        <p class="h5 text-primary mb-0"><?php echo $booking['booking_reference']; ?></p>
                    </div>
                    
                    <div class="mb-3">
                        <h6 class="text-uppercase text-muted mb-1">Booking Date</h6>
                        <p class="mb-0"><?php echo formatDate($booking['booking_date']); ?></p>
                    </div>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="border-top pt-3 mt-4">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p class="small text-muted mb-0">
                            <i class="fas fa-info-circle me-1"></i>
                            Please bring this ticket (printed or on mobile) to the event entrance.
                        </p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="small text-muted mb-0">
                            Powered by <strong>EventLynx</strong> |
                            <i class="fas fa-phone me-1"></i><?php echo SITE_PHONE; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Important Information -->
        <div class="no-print">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Important Information</h5>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li>Please arrive at least 30 minutes before the event start time.</li>
                        <li>This ticket is valid for <?php echo $booking['quantity']; ?> person<?php echo $booking['quantity'] > 1 ? 's' : ''; ?>.</li>
                        <li>Present this ticket (printed or on mobile) at the entrance.</li>
                        <li>Tickets are non-refundable and non-transferable.</li>
                        <li>For any queries, contact the event organizer or EventLynx support.</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
