<?php
require_once '../config/config.php';

$page_title = 'Login';
$error = '';
$success = '';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ' . SITE_URL);
    exit();
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = sanitize($_POST['email']);
    $password = $_POST['password'];
    
    if (empty($email) || empty($password)) {
        $error = 'Please fill in all fields.';
    } else {
        $db = getDB();
        $stmt = $db->prepare("SELECT id, username, email, password, first_name, last_name, is_admin FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();
        
        if ($user && password_verify($password, $user['password'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['username'] = $user['username'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['first_name'] = $user['first_name'];
            $_SESSION['last_name'] = $user['last_name'];
            $_SESSION['is_admin'] = $user['is_admin'];
            
            // Generate session ID for cart if not exists
            if (!isset($_SESSION['session_id'])) {
                $_SESSION['session_id'] = session_id();
            }
            
            // Transfer guest cart to user cart
            $stmt = $db->prepare("UPDATE cart SET user_id = ?, session_id = NULL WHERE session_id = ?");
            $stmt->execute([$user['id'], session_id()]);
            
            // Redirect to intended page or dashboard
            $redirect = $_GET['redirect'] ?? (isAdmin() ? '/admin/' : '/user/dashboard.php');
            header('Location: ' . SITE_URL . $redirect);
            exit();
        } else {
            $error = 'Invalid email or password.';
        }
    }
}

include '../includes/header.php';
?>

<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-lg border-0 rounded-lg">
                <div class="card-header bg-primary text-white text-center py-4">
                    <h3 class="mb-0"><i class="fas fa-sign-in-alt me-2"></i>Login to EventLynx</h3>
                </div>
                <div class="card-body p-5">
                    <?php if ($error): ?>
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ($success): ?>
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="form-floating mb-3">
                            <input type="email" class="form-control" id="email" name="email" 
                                   placeholder="<EMAIL>" required value="<?php echo htmlspecialchars($email ?? ''); ?>">
                            <label for="email">Email Address</label>
                            <div class="invalid-feedback">
                                Please provide a valid email address.
                            </div>
                        </div>
                        
                        <div class="form-floating mb-4">
                            <input type="password" class="form-control" id="password" name="password" 
                                   placeholder="Password" required>
                            <label for="password">Password</label>
                            <div class="invalid-feedback">
                                Please provide your password.
                            </div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-sign-in-alt me-2"></i>Login
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center py-3">
                    <div class="small">
                        Don't have an account? 
                        <a href="register.php" class="text-decoration-none">Create one here</a>
                    </div>
                </div>
            </div>
            
            <!-- Demo Credentials -->
            <div class="card mt-3 border-info">
                <div class="card-body">
                    <h6 class="card-title text-info"><i class="fas fa-info-circle me-2"></i>Demo Credentials</h6>
                    <p class="card-text small mb-2">
                        <strong>Admin:</strong> <EMAIL> / admin123<br>
                        <strong>User:</strong> Register a new account or use admin credentials
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
