<?php
/**
 * Database Session Manager for EventLynx
 * Handles user sessions in database instead of files
 */

class SessionManager {
    private $db;
    private $session_lifetime;
    private $session_id;
    
    public function __construct($database_connection, $lifetime = 7200) {
        $this->db = $database_connection;
        $this->session_lifetime = $lifetime; // 2 hours default
        
        // Set custom session handlers
        session_set_save_handler(
            [$this, 'open'],
            [$this, 'close'],
            [$this, 'read'],
            [$this, 'write'],
            [$this, 'destroy'],
            [$this, 'gc']
        );
        
        // Configure session settings
        ini_set('session.gc_probability', 1);
        ini_set('session.gc_divisor', 100);
        ini_set('session.gc_maxlifetime', $this->session_lifetime);
        ini_set('session.cookie_lifetime', 0); // Session cookie
        ini_set('session.cookie_httponly', 1);
        ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
        ini_set('session.use_strict_mode', 1);
    }
    
    /**
     * Open session
     */
    public function open($save_path, $session_name) {
        return true;
    }
    
    /**
     * Close session
     */
    public function close() {
        return true;
    }
    
    /**
     * Read session data
     */
    public function read($session_id) {
        try {
            $this->session_id = $session_id;
            
            $stmt = $this->db->prepare("
                SELECT session_data, user_id, expires_at
                FROM user_sessions
                WHERE id = ? AND is_active = 1 AND expires_at > NOW()
            ");
            $stmt->execute([$session_id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result) {
                // Update last activity
                $this->updateLastActivity($session_id);
                return $result['session_data'] ?? '';
            }
            
            return '';
        } catch (Exception $e) {
            error_log("Session read error: " . $e->getMessage());
            return '';
        }
    }
    
    /**
     * Write session data
     */
    public function write($session_id, $session_data) {
        try {
            $expires_at = date('Y-m-d H:i:s', time() + $this->session_lifetime);
            $ip_address = $this->getClientIP();
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $user_id = $_SESSION['user_id'] ?? null;
            
            // Get device and location info
            $device_info = $this->getDeviceInfo();
            $location_info = $this->getLocationInfo($ip_address);
            
            $stmt = $this->db->prepare("
                INSERT INTO user_sessions (
                    id, user_id, ip_address, user_agent, session_data, 
                    expires_at, device_info, location_info
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE
                    session_data = VALUES(session_data),
                    last_activity = CURRENT_TIMESTAMP,
                    expires_at = VALUES(expires_at),
                    user_id = VALUES(user_id)
            ");
            
            return $stmt->execute([
                $session_id,
                $user_id,
                $ip_address,
                $user_agent,
                $session_data,
                $expires_at,
                json_encode($device_info),
                json_encode($location_info)
            ]);
        } catch (Exception $e) {
            error_log("Session write error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Destroy session
     */
    public function destroy($session_id) {
        try {
            $stmt = $this->db->prepare("
                UPDATE user_sessions 
                SET is_active = 0 
                WHERE id = ?
            ");
            return $stmt->execute([$session_id]);
        } catch (Exception $e) {
            error_log("Session destroy error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Garbage collection
     */
    public function gc($max_lifetime) {
        try {
            $stmt = $this->db->prepare("
                DELETE FROM user_sessions 
                WHERE expires_at < NOW() OR is_active = 0
            ");
            return $stmt->execute();
        } catch (Exception $e) {
            error_log("Session GC error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update last activity timestamp
     */
    private function updateLastActivity($session_id) {
        try {
            $stmt = $this->db->prepare("
                UPDATE user_sessions 
                SET last_activity = CURRENT_TIMESTAMP 
                WHERE id = ?
            ");
            $stmt->execute([$session_id]);
        } catch (Exception $e) {
            error_log("Update activity error: " . $e->getMessage());
        }
    }
    
    /**
     * Get client IP address
     */
    private function getClientIP() {
        $ip_keys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ip_keys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
    }
    
    /**
     * Get device information
     */
    private function getDeviceInfo() {
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        return [
            'browser' => $this->getBrowser($user_agent),
            'os' => $this->getOS($user_agent),
            'device' => $this->getDevice($user_agent),
            'is_mobile' => $this->isMobile($user_agent)
        ];
    }
    
    /**
     * Get location information (basic)
     */
    private function getLocationInfo($ip) {
        // Basic location detection - you can integrate with IP geolocation services
        return [
            'ip' => $ip,
            'country' => 'Unknown',
            'city' => 'Unknown',
            'timezone' => date_default_timezone_get()
        ];
    }
    
    /**
     * Detect browser
     */
    private function getBrowser($user_agent) {
        $browsers = [
            'Chrome' => '/Chrome/i',
            'Firefox' => '/Firefox/i',
            'Safari' => '/Safari/i',
            'Edge' => '/Edge/i',
            'Opera' => '/Opera/i'
        ];
        
        foreach ($browsers as $browser => $pattern) {
            if (preg_match($pattern, $user_agent)) {
                return $browser;
            }
        }
        
        return 'Unknown';
    }
    
    /**
     * Detect operating system
     */
    private function getOS($user_agent) {
        $os_array = [
            'Windows' => '/Windows/i',
            'Mac' => '/Mac/i',
            'Linux' => '/Linux/i',
            'Android' => '/Android/i',
            'iOS' => '/iPhone|iPad/i'
        ];
        
        foreach ($os_array as $os => $pattern) {
            if (preg_match($pattern, $user_agent)) {
                return $os;
            }
        }
        
        return 'Unknown';
    }
    
    /**
     * Detect device type
     */
    private function getDevice($user_agent) {
        if (preg_match('/Mobile|Android|iPhone|iPad/i', $user_agent)) {
            return 'Mobile';
        } elseif (preg_match('/Tablet|iPad/i', $user_agent)) {
            return 'Tablet';
        }
        
        return 'Desktop';
    }
    
    /**
     * Check if mobile device
     */
    private function isMobile($user_agent) {
        return preg_match('/Mobile|Android|iPhone/i', $user_agent) ? true : false;
    }
    
    /**
     * Get active sessions for a user
     */
    public function getUserSessions($user_id) {
        try {
            $stmt = $this->db->prepare("
                SELECT id, ip_address, device_info, location_info, 
                       last_activity, created_at
                FROM user_sessions 
                WHERE user_id = ? AND is_active = 1 AND expires_at > NOW()
                ORDER BY last_activity DESC
            ");
            $stmt->execute([$user_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Get user sessions error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Terminate all sessions for a user except current
     */
    public function terminateOtherSessions($user_id, $current_session_id = null) {
        try {
            $current_session_id = $current_session_id ?? session_id();
            
            $stmt = $this->db->prepare("
                UPDATE user_sessions 
                SET is_active = 0 
                WHERE user_id = ? AND id != ? AND is_active = 1
            ");
            return $stmt->execute([$user_id, $current_session_id]);
        } catch (Exception $e) {
            error_log("Terminate sessions error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get session statistics
     */
    public function getSessionStats() {
        try {
            $stmt = $this->db->query("
                SELECT 
                    COUNT(*) as total_active,
                    COUNT(DISTINCT user_id) as unique_users,
                    COUNT(CASE WHEN last_activity > DATE_SUB(NOW(), INTERVAL 5 MINUTE) THEN 1 END) as active_5min
                FROM user_sessions 
                WHERE is_active = 1 AND expires_at > NOW()
            ");
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Session stats error: " . $e->getMessage());
            return ['total_active' => 0, 'unique_users' => 0, 'active_5min' => 0];
        }
    }
}
?>
