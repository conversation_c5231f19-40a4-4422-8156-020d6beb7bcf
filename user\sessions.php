<?php
require_once '../config/config.php';

// Require login
requireLogin();

$page_title = 'Active Sessions';
$user_id = $_SESSION['user_id'];

// Handle session termination
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
    if ($_POST['action'] == 'terminate_all') {
        if (terminateOtherSessions($user_id)) {
            $success_message = "All other sessions have been terminated.";
        } else {
            $error_message = "Failed to terminate sessions.";
        }
    }
}

// Get user's active sessions
$sessions = getUserSessions($user_id);
$current_session_id = session_id();

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 sidebar">
            <div class="d-flex flex-column p-3">
                <h5 class="text-primary">User Dashboard</h5>
                <ul class="nav nav-pills flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="bookings.php">
                            <i class="fas fa-ticket-alt me-2"></i>My Bookings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../auth/profile.php">
                            <i class="fas fa-user-edit me-2"></i>Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="sessions.php">
                            <i class="fas fa-shield-alt me-2"></i>Active Sessions
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 main-content">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-shield-alt me-2"></i>Active Sessions</h2>
            </div>

            <?php if (isset($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show">
                    <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Security Info -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body">
                            <h5 class="card-title text-primary">
                                <i class="fas fa-info-circle me-2"></i>Session Security
                            </h5>
                            <p class="card-text">
                                Monitor and manage your active sessions across different devices. 
                                If you see any suspicious activity, terminate all sessions immediately.
                            </p>
                            <form method="POST" class="d-inline">
                                <input type="hidden" name="action" value="terminate_all">
                                <button type="submit" class="btn btn-danger" 
                                        onclick="return confirm('This will log you out from all other devices. Continue?')">
                                    <i class="fas fa-sign-out-alt me-2"></i>Terminate All Other Sessions
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Active Sessions -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-desktop me-2"></i>Active Sessions (<?php echo count($sessions); ?>)
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <?php if (empty($sessions)): ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-desktop fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No Active Sessions</h5>
                                    <p class="text-muted">You don't have any active sessions.</p>
                                </div>
                            <?php else: ?>
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Device</th>
                                                <th>Location</th>
                                                <th>IP Address</th>
                                                <th>Last Activity</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($sessions as $session): ?>
                                                <?php 
                                                $device_info = json_decode($session['device_info'], true);
                                                $location_info = json_decode($session['location_info'], true);
                                                $is_current = $session['id'] === $current_session_id;
                                                ?>
                                                <tr class="<?php echo $is_current ? 'table-success' : ''; ?>">
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="me-3">
                                                                <?php if ($device_info['device'] === 'Mobile'): ?>
                                                                    <i class="fas fa-mobile-alt fa-lg text-primary"></i>
                                                                <?php elseif ($device_info['device'] === 'Tablet'): ?>
                                                                    <i class="fas fa-tablet-alt fa-lg text-info"></i>
                                                                <?php else: ?>
                                                                    <i class="fas fa-desktop fa-lg text-secondary"></i>
                                                                <?php endif; ?>
                                                            </div>
                                                            <div>
                                                                <div class="fw-bold">
                                                                    <?php echo htmlspecialchars($device_info['browser'] ?? 'Unknown'); ?>
                                                                </div>
                                                                <small class="text-muted">
                                                                    <?php echo htmlspecialchars($device_info['os'] ?? 'Unknown OS'); ?> • 
                                                                    <?php echo htmlspecialchars($device_info['device'] ?? 'Unknown Device'); ?>
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <i class="fas fa-map-marker-alt text-muted me-1"></i>
                                                            <?php echo htmlspecialchars($location_info['city'] ?? 'Unknown'); ?>
                                                        </div>
                                                        <small class="text-muted">
                                                            <?php echo htmlspecialchars($location_info['country'] ?? 'Unknown'); ?>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <code class="small"><?php echo htmlspecialchars($session['ip_address']); ?></code>
                                                    </td>
                                                    <td>
                                                        <div>
                                                            <?php echo date('M j, Y', strtotime($session['last_activity'])); ?>
                                                        </div>
                                                        <small class="text-muted">
                                                            <?php echo date('g:i A', strtotime($session['last_activity'])); ?>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <?php if ($is_current): ?>
                                                            <span class="badge bg-success">
                                                                <i class="fas fa-check me-1"></i>Current Session
                                                            </span>
                                                        <?php else: ?>
                                                            <?php 
                                                            $last_activity = strtotime($session['last_activity']);
                                                            $minutes_ago = (time() - $last_activity) / 60;
                                                            ?>
                                                            <?php if ($minutes_ago < 5): ?>
                                                                <span class="badge bg-success">
                                                                    <i class="fas fa-circle me-1"></i>Active
                                                                </span>
                                                            <?php elseif ($minutes_ago < 30): ?>
                                                                <span class="badge bg-warning">
                                                                    <i class="fas fa-clock me-1"></i>Recent
                                                                </span>
                                                            <?php else: ?>
                                                                <span class="badge bg-secondary">
                                                                    <i class="fas fa-pause me-1"></i>Idle
                                                                </span>
                                                            <?php endif; ?>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Session Info -->
            <div class="row mt-4">
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-clock fa-2x text-primary mb-3"></i>
                            <h5>Session Timeout</h5>
                            <p class="text-muted">Sessions expire after 2 hours of inactivity</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center">
                            <i class="fas fa-shield-alt fa-2x text-success mb-3"></i>
                            <h5>Secure Sessions</h5>
                            <p class="text-muted">All sessions are encrypted and monitored</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.sidebar {
    background: var(--gray-50);
    min-height: calc(100vh - 76px);
}

.main-content {
    padding: 2rem;
}

.nav-pills .nav-link {
    color: var(--gray-600);
    border-radius: var(--radius-lg);
    margin-bottom: 0.5rem;
}

.nav-pills .nav-link:hover {
    background: var(--gray-200);
    color: var(--primary-color);
}

.nav-pills .nav-link.active {
    background: var(--primary-color);
    color: white;
}

.card {
    border-radius: var(--radius-xl);
}

.table th {
    border-top: none;
    font-weight: 600;
    color: var(--gray-700);
}
</style>

<?php include '../includes/footer.php'; ?>
