<?php
require_once 'config/config.php';

$page_title = 'Home';

// Get featured events
$db = getDB();
$stmt = $db->prepare("SELECT * FROM events WHERE status = 'active' AND event_date >= CURDATE() ORDER BY created_at DESC LIMIT 6");
$stmt->execute();
$featured_events = $stmt->fetchAll();

// Get event statistics
$stmt = $db->prepare("SELECT COUNT(*) as total_events FROM events WHERE status = 'active'");
$stmt->execute();
$total_events = $stmt->fetch()['total_events'];

$stmt = $db->prepare("SELECT COUNT(*) as total_bookings FROM bookings WHERE payment_status = 'completed'");
$stmt->execute();
$total_bookings = $stmt->fetch()['total_bookings'];

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h1 class="display-4 fw-bold">Discover Amazing Events</h1>
                <p class="lead">Find and book tickets for concerts, conferences, workshops, and more. Your next great experience is just a click away!</p>
                <div class="d-flex gap-3 justify-content-center">
                    <a href="events/search.php" class="btn btn-light btn-lg">
                        <i class="fas fa-search me-2"></i>Browse Events
                    </a>
                    <?php if (!isLoggedIn()): ?>
                        <a href="auth/register.php" class="btn btn-outline-light btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Join Now
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Search Form -->
<div class="container">
    <div class="search-form">
        <form action="events/search.php" method="GET" class="row g-3">
            <div class="col-md-4">
                <div class="form-floating">
                    <input type="text" class="form-control" id="search" name="search" placeholder="Search events...">
                    <label for="search">Search Events</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-floating">
                    <input type="text" class="form-control" id="location" name="location" placeholder="Location">
                    <label for="location">Location</label>
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-floating">
                    <input type="date" class="form-control" id="date" name="date" min="<?php echo date('Y-m-d'); ?>">
                    <label for="date">Date</label>
                </div>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary h-100 w-100">
                    <i class="fas fa-search"></i> Search
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Statistics Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row text-center">
            <div class="col-md-4 mb-4">
                <div class="stat-card">
                    <h3><?php echo number_format($total_events); ?></h3>
                    <p class="mb-0">Active Events</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #198754, #146c43);">
                    <h3><?php echo number_format($total_bookings); ?></h3>
                    <p class="mb-0">Happy Customers</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="stat-card" style="background: linear-gradient(135deg, #dc3545, #b02a37);">
                    <h3>24/7</h3>
                    <p class="mb-0">Customer Support</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Featured Events -->
<section class="py-5">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="display-5 fw-bold text-primary-custom">Featured Events</h2>
                <p class="lead text-muted">Don't miss out on these amazing upcoming events</p>
            </div>
        </div>
        
        <?php if (empty($featured_events)): ?>
            <div class="row">
                <div class="col-12 text-center">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        No events available at the moment. Check back soon!
                    </div>
                </div>
            </div>
        <?php else: ?>
            <div class="row">
                <?php foreach ($featured_events as $event): ?>
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card event-card h-100">
                            <?php if ($event['image_url']): ?>
                                <img src="<?php echo htmlspecialchars($event['image_url']); ?>" 
                                     class="card-img-top" alt="<?php echo htmlspecialchars($event['title']); ?>">
                            <?php else: ?>
                                <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                    <i class="fas fa-calendar-alt fa-3x text-muted"></i>
                                </div>
                            <?php endif; ?>
                            
                            <div class="card-body d-flex flex-column">
                                <div class="event-date">
                                    <i class="fas fa-calendar me-1"></i>
                                    <?php echo formatDate($event['event_date']); ?>
                                </div>
                                
                                <h5 class="card-title"><?php echo htmlspecialchars($event['title']); ?></h5>
                                
                                <p class="card-text flex-grow-1">
                                    <?php echo htmlspecialchars(substr($event['description'], 0, 100)) . '...'; ?>
                                </p>
                                
                                <div class="event-location mb-2">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?php echo htmlspecialchars($event['venue'] . ', ' . $event['city']); ?>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="event-price"><?php echo formatCurrency($event['price']); ?></span>
                                    <a href="events/details.php?id=<?php echo $event['id']; ?>" 
                                       class="btn btn-primary">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="row">
                <div class="col-12 text-center">
                    <a href="events/search.php" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-th-large me-2"></i>View All Events
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</section>

<!-- Features Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row mb-5">
            <div class="col-12 text-center">
                <h2 class="display-5 fw-bold text-primary-custom">Why Choose EventLynx?</h2>
                <p class="lead text-muted">We make event booking simple and secure</p>
            </div>
        </div>
        
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="text-center">
                    <div class="bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-shield-alt fa-2x"></i>
                    </div>
                    <h4>Secure Booking</h4>
                    <p class="text-muted">Your personal and payment information is always protected with industry-standard security.</p>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="text-center">
                    <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-mobile-alt fa-2x"></i>
                    </div>
                    <h4>Mobile Tickets</h4>
                    <p class="text-muted">Get instant access to your tickets on your mobile device. No need to print anything!</p>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="text-center">
                    <div class="bg-info text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" 
                         style="width: 80px; height: 80px;">
                        <i class="fas fa-headset fa-2x"></i>
                    </div>
                    <h4>24/7 Support</h4>
                    <p class="text-muted">Our customer support team is always ready to help you with any questions or issues.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
