/**
 * EventLynx Main JavaScript File
 * Contains common functionality and AJAX handlers
 */

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeEventHandlers();
    initializeTooltips();
    initializeFormValidation();
});

/**
 * Initialize event handlers
 */
function initializeEventHandlers() {
    // Add to cart functionality
    const addToCartButtons = document.querySelectorAll('.add-to-cart');
    addToCartButtons.forEach(button => {
        button.addEventListener('click', handleAddToCart);
    });

    // Remove from cart functionality
    const removeFromCartButtons = document.querySelectorAll('.remove-from-cart');
    removeFromCartButtons.forEach(button => {
        button.addEventListener('click', handleRemoveFromCart);
    });

    // Update cart quantity
    const quantityInputs = document.querySelectorAll('.cart-quantity');
    quantityInputs.forEach(input => {
        input.addEventListener('change', handleUpdateQuantity);
    });

    // Search form auto-submit
    const searchInputs = document.querySelectorAll('.search-input');
    searchInputs.forEach(input => {
        input.addEventListener('input', debounce(handleSearch, 500));
    });
}

/**
 * Initialize Bootstrap tooltips
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Initialize form validation
 */
function initializeFormValidation() {
    const forms = document.querySelectorAll('.needs-validation');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
}

/**
 * Handle add to cart
 */
function handleAddToCart(event) {
    event.preventDefault();
    const button = event.target;
    const eventId = button.dataset.eventId;
    const quantity = document.getElementById('quantity')?.value || 1;

    // Show loading state
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
    button.disabled = true;

    // AJAX request to add to cart
    fetch('/EventLynx/cart/add_to_cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `event_id=${eventId}&quantity=${quantity}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('Item added to cart successfully!', 'success');
            updateCartCount(data.cart_count);
        } else {
            showAlert(data.message || 'Failed to add item to cart', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred. Please try again.', 'danger');
    })
    .finally(() => {
        // Restore button state
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

/**
 * Handle remove from cart
 */
function handleRemoveFromCart(event) {
    event.preventDefault();
    const button = event.target;
    const cartId = button.dataset.cartId;

    if (!confirm('Are you sure you want to remove this item from your cart?')) {
        return;
    }

    // AJAX request to remove from cart
    fetch('/EventLynx/cart/remove_from_cart.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `cart_id=${cartId}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Remove the cart item from DOM
            const cartItem = button.closest('.cart-item');
            cartItem.remove();
            showAlert('Item removed from cart', 'success');
            updateCartCount(data.cart_count);
            updateCartTotal(data.cart_total);
        } else {
            showAlert(data.message || 'Failed to remove item', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred. Please try again.', 'danger');
    });
}

/**
 * Handle quantity update
 */
function handleUpdateQuantity(event) {
    const input = event.target;
    const cartId = input.dataset.cartId;
    const quantity = input.value;

    if (quantity < 1) {
        input.value = 1;
        return;
    }

    // AJAX request to update quantity
    fetch('/EventLynx/cart/update_quantity.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `cart_id=${cartId}&quantity=${quantity}`
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateCartTotal(data.cart_total);
            // Update item total
            const itemTotal = input.closest('.cart-item').querySelector('.item-total');
            if (itemTotal) {
                itemTotal.textContent = data.item_total;
            }
        } else {
            showAlert(data.message || 'Failed to update quantity', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred. Please try again.', 'danger');
    });
}

/**
 * Handle search
 */
function handleSearch(event) {
    const searchTerm = event.target.value;
    const searchForm = event.target.closest('form');
    
    if (searchTerm.length >= 3 || searchTerm.length === 0) {
        searchForm.submit();
    }
}

/**
 * Update cart count in navbar
 */
function updateCartCount(count) {
    const cartBadge = document.querySelector('.navbar .badge');
    if (cartBadge) {
        if (count > 0) {
            cartBadge.textContent = count;
            cartBadge.style.display = 'inline';
        } else {
            cartBadge.style.display = 'none';
        }
    }
}

/**
 * Update cart total
 */
function updateCartTotal(total) {
    const cartTotalElements = document.querySelectorAll('.cart-total');
    cartTotalElements.forEach(element => {
        element.textContent = total;
    });
}

/**
 * Show alert message
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alert-container') || createAlertContainer();
    
    const alert = document.createElement('div');
    alert.className = `alert alert-${type} alert-dismissible fade show`;
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    alertContainer.appendChild(alert);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}

/**
 * Create alert container if it doesn't exist
 */
function createAlertContainer() {
    const container = document.createElement('div');
    container.id = 'alert-container';
    container.className = 'position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
}

/**
 * Debounce function
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Format currency
 */
function formatCurrency(amount) {
    return Math.round(amount).toLocaleString() + ' CFA';
}

/**
 * Validate email format
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Show loading spinner
 */
function showLoading(element) {
    element.innerHTML = '<div class="spinner"></div>';
}

/**
 * Hide loading spinner
 */
function hideLoading(element, originalContent) {
    element.innerHTML = originalContent;
}
