<?php
/**
 * Setup Database Sessions for EventLynx
 * Run this script once to add session management to your existing database
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'eventlynx';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Setup Database Sessions - EventLynx</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
</head>
<body class='bg-light'>
    <div class='container mt-5'>
        <div class='row justify-content-center'>
            <div class='col-md-8'>
                <div class='card shadow'>
                    <div class='card-header bg-primary text-white'>
                        <h3 class='mb-0'><i class='fas fa-database me-2'></i>Database Session Setup</h3>
                    </div>
                    <div class='card-body'>";

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='alert alert-success'>
            <i class='fas fa-check-circle me-2'></i>Database connection successful!
          </div>";
    
    // Check if sessions table already exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_sessions'");
    if ($stmt->rowCount() > 0) {
        echo "<div class='alert alert-warning'>
                <i class='fas fa-exclamation-triangle me-2'></i>
                Sessions table already exists. Checking structure...
              </div>";
        
        // Check table structure
        $stmt = $pdo->query("DESCRIBE user_sessions");
        $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<div class='alert alert-info'>
                <h5>Existing table columns:</h5>
                <ul class='mb-0'>";
        foreach ($columns as $column) {
            echo "<li>$column</li>";
        }
        echo "</ul></div>";
        
    } else {
        echo "<div class='alert alert-info'>
                <i class='fas fa-info-circle me-2'></i>
                Creating user_sessions table...
              </div>";
        
        // Create sessions table
        $sql = "
        CREATE TABLE user_sessions (
            id VARCHAR(128) NOT NULL PRIMARY KEY,
            user_id INT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            session_data LONGTEXT,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            device_info TEXT,
            location_info TEXT,
            INDEX idx_user_id (user_id),
            INDEX idx_last_activity (last_activity),
            INDEX idx_expires_at (expires_at),
            INDEX idx_is_active (is_active),
            INDEX idx_cleanup (expires_at, is_active),
            INDEX idx_user_active (user_id, is_active, last_activity),
            FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
        )";
        
        $pdo->exec($sql);
        
        echo "<div class='alert alert-success'>
                <i class='fas fa-check-circle me-2'></i>
                Sessions table created successfully!
              </div>";
    }
    
    // Test session functionality
    echo "<div class='alert alert-info'>
            <i class='fas fa-cog me-2'></i>Testing session functionality...
          </div>";
    
    // Test insert
    $test_session_id = 'test_' . uniqid();
    $stmt = $pdo->prepare("
        INSERT INTO user_sessions (
            id, ip_address, user_agent, session_data, expires_at, device_info, location_info
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    
    $test_data = [
        $test_session_id,
        '127.0.0.1',
        'Test User Agent',
        'test_data',
        date('Y-m-d H:i:s', time() + 3600),
        json_encode(['browser' => 'Test', 'os' => 'Test', 'device' => 'Desktop']),
        json_encode(['country' => 'Test', 'city' => 'Test'])
    ];
    
    if ($stmt->execute($test_data)) {
        echo "<div class='alert alert-success'>
                <i class='fas fa-check-circle me-2'></i>
                Session insert test successful!
              </div>";
        
        // Test read
        $stmt = $pdo->prepare("SELECT * FROM user_sessions WHERE id = ?");
        $stmt->execute([$test_session_id]);
        $result = $stmt->fetch();
        
        if ($result) {
            echo "<div class='alert alert-success'>
                    <i class='fas fa-check-circle me-2'></i>
                    Session read test successful!
                  </div>";
        }
        
        // Clean up test data
        $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE id = ?");
        $stmt->execute([$test_session_id]);
        
        echo "<div class='alert alert-success'>
                <i class='fas fa-check-circle me-2'></i>
                Test cleanup successful!
              </div>";
    }
    
    // Show current session stats
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_sessions,
            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_sessions,
            COUNT(DISTINCT user_id) as unique_users
        FROM user_sessions
    ");
    $stats = $stmt->fetch();
    
    echo "<div class='row mt-4'>
            <div class='col-md-4'>
                <div class='card bg-primary text-white'>
                    <div class='card-body text-center'>
                        <h3>" . $stats['total_sessions'] . "</h3>
                        <p class='mb-0'>Total Sessions</p>
                    </div>
                </div>
            </div>
            <div class='col-md-4'>
                <div class='card bg-success text-white'>
                    <div class='card-body text-center'>
                        <h3>" . $stats['active_sessions'] . "</h3>
                        <p class='mb-0'>Active Sessions</p>
                    </div>
                </div>
            </div>
            <div class='col-md-4'>
                <div class='card bg-info text-white'>
                    <div class='card-body text-center'>
                        <h3>" . $stats['unique_users'] . "</h3>
                        <p class='mb-0'>Unique Users</p>
                    </div>
                </div>
            </div>
          </div>";
    
    echo "<div class='alert alert-success mt-4'>
            <h4><i class='fas fa-check-circle me-2'></i>Setup Complete!</h4>
            <p class='mb-3'>Database session management has been successfully configured for EventLynx.</p>
            <h5>What's New:</h5>
            <ul>
                <li><strong>Database Sessions:</strong> Sessions are now stored in the database instead of files</li>
                <li><strong>Device Tracking:</strong> Track user devices, browsers, and operating systems</li>
                <li><strong>Security:</strong> Enhanced session security with IP tracking and device fingerprinting</li>
                <li><strong>Management:</strong> Users can view and manage their active sessions</li>
                <li><strong>Admin Panel:</strong> Administrators can monitor all active sessions</li>
            </ul>
            <h5>New Features Available:</h5>
            <ul>
                <li><strong>User Sessions Page:</strong> <code>/user/sessions.php</code></li>
                <li><strong>Admin Sessions Page:</strong> <code>/admin/sessions.php</code></li>
                <li><strong>Session Termination:</strong> Users can terminate all other sessions</li>
                <li><strong>Activity Monitoring:</strong> Real-time session activity tracking</li>
            </ul>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h4><i class='fas fa-exclamation-circle me-2'></i>Database Error</h4>
            <p><strong>Error:</strong> " . $e->getMessage() . "</p>
            <h5>Common Solutions:</h5>
            <ul>
                <li>Make sure XAMPP MySQL is running</li>
                <li>Verify database credentials</li>
                <li>Check if 'eventlynx' database exists</li>
                <li>Ensure users table exists (run main setup first)</li>
            </ul>
          </div>";
}

echo "        <div class='text-center mt-4'>
                <a href='index.php' class='btn btn-primary me-2'>
                    <i class='fas fa-home me-2'></i>Go to Homepage
                </a>
                <a href='admin/sessions.php' class='btn btn-success me-2'>
                    <i class='fas fa-shield-alt me-2'></i>View Session Management
                </a>
                <a href='user/sessions.php' class='btn btn-info'>
                    <i class='fas fa-user me-2'></i>User Sessions
                </a>
              </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
