<?php
/**
 * Update Currency Script
 * Convert existing USD prices to CFA and update phone numbers
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'eventlynx';

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>💱 Currency Update Tool</h2>";
    
    // Get current events
    $stmt = $pdo->query("SELECT id, title, price, organizer_phone, city, country FROM events");
    $events = $stmt->fetchAll();
    
    if (empty($events)) {
        echo "<p>❌ No events found in database.</p>";
        echo "<p><a href='setup.php'>Run Setup</a> to create sample events.</p>";
    } else {
        echo "<h3>📊 Current Events:</h3>";
        echo "<table border='1' cellpadding='10' cellspacing='0' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th>ID</th><th>Title</th><th>Current Price</th><th>Phone</th><th>Location</th><th>Action</th></tr>";
        
        foreach ($events as $event) {
            echo "<tr>";
            echo "<td>" . $event['id'] . "</td>";
            echo "<td>" . htmlspecialchars($event['title']) . "</td>";
            echo "<td>" . $event['price'] . "</td>";
            echo "<td>" . htmlspecialchars($event['organizer_phone'] ?? 'Not set') . "</td>";
            echo "<td>" . htmlspecialchars($event['city'] . ', ' . $event['country']) . "</td>";
            echo "<td>";
            echo "<form method='POST' style='display: inline;'>";
            echo "<input type='hidden' name='event_id' value='" . $event['id'] . "'>";
            echo "<button type='submit' name='update_event' style='background: #28a745; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;'>Update</button>";
            echo "</form>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Handle individual event updates
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_event'])) {
            $event_id = (int)$_POST['event_id'];
            
            // Get current event
            $stmt = $pdo->prepare("SELECT price FROM events WHERE id = ?");
            $stmt->execute([$event_id]);
            $current_event = $stmt->fetch();
            
            if ($current_event) {
                $current_price = $current_event['price'];
                
                // Convert USD to CFA (approximate rate: 1 USD = 500 CFA)
                $new_price = round($current_price * 500);
                
                // Update event with CFA price, phone, and Cameroon location
                $stmt = $pdo->prepare("
                    UPDATE events 
                    SET price = ?, 
                        organizer_phone = ?, 
                        country = 'Cameroon'
                    WHERE id = ?
                ");
                
                if ($stmt->execute([$new_price, '+237677069985', $event_id])) {
                    echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0; color: #155724;'>";
                    echo "✅ Event ID $event_id updated: $current_price USD → $new_price CFA";
                    echo "</div>";
                    echo "<script>setTimeout(function(){ location.reload(); }, 2000);</script>";
                }
            }
        }
        
        // Bulk update button
        echo "<h3>🚀 Bulk Actions:</h3>";
        echo "<form method='POST' style='margin: 20px 0;'>";
        echo "<button type='submit' name='update_all' style='background: #dc3545; color: white; border: none; padding: 15px 30px; border-radius: 5px; cursor: pointer; font-size: 16px;' onclick='return confirm(\"Update all events to use CFA currency and Cameroon phone number?\")'>Update All Events</button>";
        echo "</form>";
        
        // Handle bulk update
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_all'])) {
            $updated_count = 0;
            
            foreach ($events as $event) {
                $current_price = $event['price'];
                $new_price = round($current_price * 500); // Convert to CFA
                
                $stmt = $pdo->prepare("
                    UPDATE events 
                    SET price = ?, 
                        organizer_phone = ?, 
                        country = 'Cameroon'
                    WHERE id = ?
                ");
                
                if ($stmt->execute([$new_price, '+237677069985', $event['id']])) {
                    $updated_count++;
                }
            }
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0; color: #155724;'>";
            echo "<h3>🎉 Bulk Update Complete!</h3>";
            echo "<p>✅ Updated $updated_count events</p>";
            echo "<p>✅ All prices converted to CFA</p>";
            echo "<p>✅ All phone numbers updated to +237677069985</p>";
            echo "<p>✅ All countries set to Cameroon</p>";
            echo "</div>";
            echo "<script>setTimeout(function(){ location.reload(); }, 3000);</script>";
        }
    }
    
    // Show conversion rates
    echo "<div style='background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h4>💱 Conversion Information:</h4>";
    echo "<ul>";
    echo "<li><strong>Currency:</strong> USD → CFA (Central African Franc)</li>";
    echo "<li><strong>Rate:</strong> 1 USD ≈ 500 CFA (approximate)</li>";
    echo "<li><strong>Phone:</strong> Updated to +237677069985 (Cameroon)</li>";
    echo "<li><strong>Country:</strong> Updated to Cameroon</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (PDOException $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border: 1px solid #f5c6cb; color: #721c24;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Update Currency - EventLynx</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #007bff;
            text-align: center;
            margin-bottom: 30px;
        }
        table {
            font-size: 14px;
        }
        .links {
            text-align: center;
            margin: 30px 0;
        }
        .links a {
            display: inline-block;
            margin: 0 10px;
            padding: 10px 20px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💱 Currency Update Tool</h1>
        
        <div class="links">
            <a href="index.php">🏠 Homepage</a>
            <a href="admin/">⚙️ Admin Panel</a>
            <a href="events/search.php">🔍 Browse Events</a>
        </div>
        
        <div style="margin-top: 30px; padding: 15px; background: #fff3cd; border-radius: 5px; border: 1px solid #ffeaa7;">
            <h4>📝 What this tool does:</h4>
            <ul>
                <li>Converts existing USD prices to CFA francs</li>
                <li>Updates all phone numbers to +237677069985</li>
                <li>Changes country to Cameroon for all events</li>
                <li>Updates the currency display throughout the system</li>
            </ul>
        </div>
    </div>
</body>
</html>
