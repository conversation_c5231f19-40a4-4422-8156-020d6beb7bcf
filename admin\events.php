<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'Manage Events';
$error = '';
$success = '';

$db = getDB();

// Handle actions
$action = $_GET['action'] ?? 'list';
$event_id = $_GET['id'] ?? 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['add_event'])) {
        // Add new event
        $title = sanitize($_POST['title']);
        $description = sanitize($_POST['description']);
        $event_date = $_POST['event_date'];
        $event_time = $_POST['event_time'];
        $venue = sanitize($_POST['venue']);
        $address = sanitize($_POST['address']);
        $city = sanitize($_POST['city']);
        $country = sanitize($_POST['country']);
        $organizer_name = sanitize($_POST['organizer_name']);
        $organizer_email = sanitize($_POST['organizer_email']);
        $organizer_phone = sanitize($_POST['organizer_phone']);
        $price = (float)$_POST['price'];
        $total_tickets = (int)$_POST['total_tickets'];
        $category = sanitize($_POST['category']);
        $image_url = sanitize($_POST['image_url']);
        
        if (empty($title) || empty($event_date) || empty($venue) || $price < 0 || $total_tickets < 1) {
            $error = 'Please fill in all required fields with valid values.';
        } else {
            $stmt = $db->prepare("
                INSERT INTO events (title, description, event_date, event_time, venue, address, city, country, 
                                  organizer_name, organizer_email, organizer_phone, price, total_tickets, 
                                  available_tickets, category, image_url) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            if ($stmt->execute([$title, $description, $event_date, $event_time, $venue, $address, $city, $country,
                               $organizer_name, $organizer_email, $organizer_phone, $price, $total_tickets, 
                               $total_tickets, $category, $image_url])) {
                logAdminActivity($_SESSION['user_id'], 'CREATE_EVENT', 'event', $db->lastInsertId(), "Created event: $title");
                $success = 'Event created successfully!';
                $action = 'list';
            } else {
                $error = 'Failed to create event.';
            }
        }
    } elseif (isset($_POST['update_event'])) {
        // Update existing event
        $event_id = (int)$_POST['event_id'];
        $title = sanitize($_POST['title']);
        $description = sanitize($_POST['description']);
        $event_date = $_POST['event_date'];
        $event_time = $_POST['event_time'];
        $venue = sanitize($_POST['venue']);
        $address = sanitize($_POST['address']);
        $city = sanitize($_POST['city']);
        $country = sanitize($_POST['country']);
        $organizer_name = sanitize($_POST['organizer_name']);
        $organizer_email = sanitize($_POST['organizer_email']);
        $organizer_phone = sanitize($_POST['organizer_phone']);
        $price = (float)$_POST['price'];
        $total_tickets = (int)$_POST['total_tickets'];
        $category = sanitize($_POST['category']);
        $image_url = sanitize($_POST['image_url']);
        $status = $_POST['status'];
        
        $stmt = $db->prepare("
            UPDATE events SET title=?, description=?, event_date=?, event_time=?, venue=?, address=?, city=?, 
                            country=?, organizer_name=?, organizer_email=?, organizer_phone=?, price=?, 
                            total_tickets=?, category=?, image_url=?, status=? 
            WHERE id=?
        ");
        
        if ($stmt->execute([$title, $description, $event_date, $event_time, $venue, $address, $city, $country,
                           $organizer_name, $organizer_email, $organizer_phone, $price, $total_tickets, 
                           $category, $image_url, $status, $event_id])) {
            logAdminActivity($_SESSION['user_id'], 'UPDATE_EVENT', 'event', $event_id, "Updated event: $title");
            $success = 'Event updated successfully!';
            $action = 'list';
        } else {
            $error = 'Failed to update event.';
        }
    }
}

// Handle delete action
if ($action == 'delete' && $event_id) {
    $stmt = $db->prepare("SELECT title FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch();
    
    if ($event) {
        $stmt = $db->prepare("DELETE FROM events WHERE id = ?");
        if ($stmt->execute([$event_id])) {
            logAdminActivity($_SESSION['user_id'], 'DELETE_EVENT', 'event', $event_id, "Deleted event: " . $event['title']);
            $success = 'Event deleted successfully!';
        } else {
            $error = 'Failed to delete event.';
        }
    }
    $action = 'list';
}

// Get event for editing
$event = null;
if ($action == 'edit' && $event_id) {
    $stmt = $db->prepare("SELECT * FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch();
    if (!$event) {
        $action = 'list';
        $error = 'Event not found.';
    }
}

// Get events list
if ($action == 'list') {
    $search = $_GET['search'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    $page = (int)($_GET['page'] ?? 1);
    $per_page = 10;
    $offset = ($page - 1) * $per_page;
    
    $where_conditions = [];
    $params = [];
    
    if (!empty($search)) {
        $where_conditions[] = "(title LIKE ? OR venue LIKE ? OR city LIKE ?)";
        $search_term = "%$search%";
        $params[] = $search_term;
        $params[] = $search_term;
        $params[] = $search_term;
    }
    
    if (!empty($status_filter)) {
        $where_conditions[] = "status = ?";
        $params[] = $status_filter;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // Get total count
    $count_sql = "SELECT COUNT(*) as total FROM events $where_clause";
    $stmt = $db->prepare($count_sql);
    $stmt->execute($params);
    $total_events = $stmt->fetch()['total'];
    $total_pages = ceil($total_events / $per_page);
    
    // Get events
    $sql = "SELECT * FROM events $where_clause ORDER BY created_at DESC LIMIT $per_page OFFSET $offset";
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $events = $stmt->fetchAll();
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <div class="col-md-3 col-lg-2 admin-sidebar">
            <div class="d-flex flex-column">
                <h4 class="text-white mb-4 px-3">
                    <i class="fas fa-cog me-2"></i>Admin Panel
                </h4>
                
                <nav class="nav flex-column">
                    <a class="nav-link" href="index.php">
                        <i class="fas fa-tachometer-alt me-2"></i>Dashboard
                    </a>
                    <a class="nav-link active" href="events.php">
                        <i class="fas fa-calendar-alt me-2"></i>Manage Events
                    </a>
                    <a class="nav-link" href="bookings.php">
                        <i class="fas fa-ticket-alt me-2"></i>View Bookings
                    </a>
                    <a class="nav-link" href="users.php">
                        <i class="fas fa-users me-2"></i>Manage Users
                    </a>
                    <a class="nav-link" href="reports.php">
                        <i class="fas fa-chart-bar me-2"></i>Reports
                    </a>
                    <hr class="text-white-50">
                    <a class="nav-link" href="<?php echo SITE_URL; ?>">
                        <i class="fas fa-home me-2"></i>Back to Site
                    </a>
                    <a class="nav-link" href="../auth/logout.php">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9 col-lg-10 admin-content">
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error; ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                </div>
            <?php endif; ?>

            <?php if ($action == 'list'): ?>
                <!-- Events List -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 text-primary">
                        <i class="fas fa-calendar-alt me-2"></i>Manage Events
                    </h1>
                    <a href="?action=add" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>Add New Event
                    </a>
                </div>

                <!-- Search and Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <input type="hidden" name="action" value="list">
                            <div class="col-md-4">
                                <input type="text" class="form-control" name="search" placeholder="Search events..." 
                                       value="<?php echo htmlspecialchars($search ?? ''); ?>">
                            </div>
                            <div class="col-md-3">
                                <select name="status" class="form-select">
                                    <option value="">All Status</option>
                                    <option value="active" <?php echo ($status_filter ?? '') === 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo ($status_filter ?? '') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                    <option value="cancelled" <?php echo ($status_filter ?? '') === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-search"></i> Search
                                </button>
                            </div>
                            <div class="col-md-3">
                                <?php if (!empty($search) || !empty($status_filter)): ?>
                                    <a href="?action=list" class="btn btn-outline-secondary w-100">Clear Filters</a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Events Table -->
                <div class="card">
                    <div class="card-body">
                        <?php if (empty($events)): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No events found</h5>
                                <a href="?action=add" class="btn btn-primary">Add Your First Event</a>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Event</th>
                                            <th>Date & Time</th>
                                            <th>Venue</th>
                                            <th>Price</th>
                                            <th>Tickets</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($events as $event): ?>
                                            <tr>
                                                <td>
                                                    <strong><?php echo htmlspecialchars($event['title']); ?></strong>
                                                    <?php if ($event['category']): ?>
                                                        <br><small class="text-muted"><?php echo htmlspecialchars($event['category']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php echo formatDate($event['event_date']); ?><br>
                                                    <small class="text-muted"><?php echo formatTime($event['event_time']); ?></small>
                                                </td>
                                                <td>
                                                    <?php echo htmlspecialchars($event['venue']); ?><br>
                                                    <small class="text-muted"><?php echo htmlspecialchars($event['city']); ?></small>
                                                </td>
                                                <td><?php echo formatCurrency($event['price']); ?></td>
                                                <td>
                                                    <?php echo $event['available_tickets']; ?> / <?php echo $event['total_tickets']; ?>
                                                    <?php if ($event['available_tickets'] == 0): ?>
                                                        <br><small class="text-danger">Sold Out</small>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <span class="badge bg-<?php echo $event['status'] === 'active' ? 'success' : ($event['status'] === 'inactive' ? 'warning' : 'danger'); ?>">
                                                        <?php echo ucfirst($event['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="?action=edit&id=<?php echo $event['id']; ?>" class="btn btn-outline-primary" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="<?php echo SITE_URL; ?>/events/details.php?id=<?php echo $event['id']; ?>" 
                                                           class="btn btn-outline-info" title="View" target="_blank">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="?action=delete&id=<?php echo $event['id']; ?>" 
                                                           class="btn btn-outline-danger" title="Delete"
                                                           onclick="return confirm('Are you sure you want to delete this event?')">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <?php if ($total_pages > 1): ?>
                                <nav class="mt-4">
                                    <ul class="pagination justify-content-center">
                                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                            <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                                <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                                    <?php echo $i; ?>
                                                </a>
                                            </li>
                                        <?php endfor; ?>
                                    </ul>
                                </nav>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>

            <?php elseif ($action == 'add' || $action == 'edit'): ?>
                <!-- Add/Edit Event Form -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3 text-primary">
                        <i class="fas fa-<?php echo $action == 'add' ? 'plus' : 'edit'; ?> me-2"></i>
                        <?php echo $action == 'add' ? 'Add New Event' : 'Edit Event'; ?>
                    </h1>
                    <a href="?action=list" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to List
                    </a>
                </div>

                <div class="card">
                    <div class="card-body">
                        <form method="POST" class="needs-validation" novalidate>
                            <?php if ($action == 'edit'): ?>
                                <input type="hidden" name="event_id" value="<?php echo $event['id']; ?>">
                            <?php endif; ?>
                            
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="title" name="title" 
                                               placeholder="Event Title" required 
                                               value="<?php echo htmlspecialchars($event['title'] ?? ''); ?>">
                                        <label for="title">Event Title *</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <select class="form-select" id="category" name="category">
                                            <option value="">Select Category</option>
                                            <option value="Technology" <?php echo ($event['category'] ?? '') === 'Technology' ? 'selected' : ''; ?>>Technology</option>
                                            <option value="Music" <?php echo ($event['category'] ?? '') === 'Music' ? 'selected' : ''; ?>>Music</option>
                                            <option value="Business" <?php echo ($event['category'] ?? '') === 'Business' ? 'selected' : ''; ?>>Business</option>
                                            <option value="Sports" <?php echo ($event['category'] ?? '') === 'Sports' ? 'selected' : ''; ?>>Sports</option>
                                            <option value="Education" <?php echo ($event['category'] ?? '') === 'Education' ? 'selected' : ''; ?>>Education</option>
                                            <option value="Entertainment" <?php echo ($event['category'] ?? '') === 'Entertainment' ? 'selected' : ''; ?>>Entertainment</option>
                                        </select>
                                        <label for="category">Category</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-floating mb-3">
                                <textarea class="form-control" id="description" name="description" 
                                          placeholder="Event Description" style="height: 100px"><?php echo htmlspecialchars($event['description'] ?? ''); ?></textarea>
                                <label for="description">Event Description</label>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input type="date" class="form-control" id="event_date" name="event_date" 
                                               required min="<?php echo date('Y-m-d'); ?>"
                                               value="<?php echo $event['event_date'] ?? ''; ?>">
                                        <label for="event_date">Event Date *</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input type="time" class="form-control" id="event_time" name="event_time" 
                                               required value="<?php echo $event['event_time'] ?? ''; ?>">
                                        <label for="event_time">Event Time *</label>
                                    </div>
                                </div>
                                <?php if ($action == 'edit'): ?>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" id="status" name="status" required>
                                                <option value="active" <?php echo ($event['status'] ?? '') === 'active' ? 'selected' : ''; ?>>Active</option>
                                                <option value="inactive" <?php echo ($event['status'] ?? '') === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                                <option value="cancelled" <?php echo ($event['status'] ?? '') === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                            </select>
                                            <label for="status">Status *</label>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="venue" name="venue" 
                                               placeholder="Venue Name" required 
                                               value="<?php echo htmlspecialchars($event['venue'] ?? ''); ?>">
                                        <label for="venue">Venue Name *</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="city" name="city" 
                                               placeholder="City" required 
                                               value="<?php echo htmlspecialchars($event['city'] ?? ''); ?>">
                                        <label for="city">City *</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="address" name="address" 
                                               placeholder="Full Address" required 
                                               value="<?php echo htmlspecialchars($event['address'] ?? ''); ?>">
                                        <label for="address">Full Address *</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="country" name="country" 
                                               placeholder="Country" required 
                                               value="<?php echo htmlspecialchars($event['country'] ?? 'USA'); ?>">
                                        <label for="country">Country *</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" id="price" name="price"
                                               placeholder="0" step="1" min="0" required
                                               value="<?php echo $event['price'] ?? ''; ?>">
                                        <label for="price">Ticket Price (CFA) *</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input type="number" class="form-control" id="total_tickets" name="total_tickets" 
                                               placeholder="100" min="1" required 
                                               value="<?php echo $event['total_tickets'] ?? ''; ?>">
                                        <label for="total_tickets">Total Tickets *</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input type="url" class="form-control" id="image_url" name="image_url" 
                                               placeholder="https://example.com/image.jpg" 
                                               value="<?php echo htmlspecialchars($event['image_url'] ?? ''); ?>">
                                        <label for="image_url">Event Image URL</label>
                                    </div>
                                </div>
                            </div>
                            
                            <h5 class="mt-4 mb-3">Organizer Information</h5>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input type="text" class="form-control" id="organizer_name" name="organizer_name" 
                                               placeholder="Organizer Name" required 
                                               value="<?php echo htmlspecialchars($event['organizer_name'] ?? ''); ?>">
                                        <label for="organizer_name">Organizer Name *</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input type="email" class="form-control" id="organizer_email" name="organizer_email" 
                                               placeholder="<EMAIL>" required 
                                               value="<?php echo htmlspecialchars($event['organizer_email'] ?? ''); ?>">
                                        <label for="organizer_email">Organizer Email *</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-floating mb-3">
                                        <input type="tel" class="form-control" id="organizer_phone" name="organizer_phone" 
                                               placeholder="Phone Number" 
                                               value="<?php echo htmlspecialchars($event['organizer_phone'] ?? ''); ?>">
                                        <label for="organizer_phone">Organizer Phone</label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="?action=list" class="btn btn-outline-secondary me-md-2">Cancel</a>
                                <button type="submit" name="<?php echo $action == 'add' ? 'add_event' : 'update_event'; ?>" 
                                        class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    <?php echo $action == 'add' ? 'Create Event' : 'Update Event'; ?>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
