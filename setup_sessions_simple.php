<?php
/**
 * Simple Session Setup for EventLynx
 * Compatible with all MySQL versions
 */

// Database configuration
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'eventlynx';

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Setup Sessions - EventLynx</title>
    <link href='https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css' rel='stylesheet'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css'>
</head>
<body class='bg-light'>
    <div class='container mt-5'>
        <div class='row justify-content-center'>
            <div class='col-md-8'>
                <div class='card shadow'>
                    <div class='card-header bg-primary text-white'>
                        <h3 class='mb-0'><i class='fas fa-database me-2'></i>Simple Session Setup</h3>
                    </div>
                    <div class='card-body'>";

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<div class='alert alert-success'>
            <i class='fas fa-check-circle me-2'></i>Database connection successful!
          </div>";
    
    // Drop table if exists (for clean setup)
    if (isset($_POST['force_recreate'])) {
        $pdo->exec("DROP TABLE IF EXISTS user_sessions");
        echo "<div class='alert alert-warning'>
                <i class='fas fa-trash me-2'></i>Existing sessions table dropped.
              </div>";
    }
    
    // Check if sessions table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'user_sessions'");
    if ($stmt->rowCount() > 0) {
        echo "<div class='alert alert-warning'>
                <i class='fas fa-exclamation-triangle me-2'></i>
                Sessions table already exists.
              </div>";
        
        echo "<form method='POST' class='mb-3'>
                <button type='submit' name='force_recreate' class='btn btn-warning' 
                        onclick='return confirm(\"This will delete all existing sessions. Continue?\")'>
                    <i class='fas fa-redo me-2'></i>Recreate Table
                </button>
              </form>";
    } else {
        echo "<div class='alert alert-info'>
                <i class='fas fa-info-circle me-2'></i>
                Creating user_sessions table...
              </div>";
        
        // Create sessions table with MySQL compatibility
        $sql = "CREATE TABLE user_sessions (
            id VARCHAR(128) NOT NULL PRIMARY KEY,
            user_id INT NULL,
            ip_address VARCHAR(45) NOT NULL,
            user_agent TEXT,
            session_data LONGTEXT,
            last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at DATETIME NOT NULL,
            is_active TINYINT(1) DEFAULT 1,
            device_info TEXT,
            location_info TEXT
        )";
        
        $pdo->exec($sql);
        
        // Add indexes separately for better compatibility
        $indexes = [
            "CREATE INDEX idx_user_id ON user_sessions (user_id)",
            "CREATE INDEX idx_last_activity ON user_sessions (last_activity)",
            "CREATE INDEX idx_expires_at ON user_sessions (expires_at)",
            "CREATE INDEX idx_is_active ON user_sessions (is_active)",
            "CREATE INDEX idx_cleanup ON user_sessions (expires_at, is_active)",
            "CREATE INDEX idx_user_active ON user_sessions (user_id, is_active, last_activity)"
        ];
        
        foreach ($indexes as $index_sql) {
            try {
                $pdo->exec($index_sql);
            } catch (Exception $e) {
                echo "<div class='alert alert-warning'>
                        <i class='fas fa-exclamation-triangle me-2'></i>
                        Index creation warning: " . $e->getMessage() . "
                      </div>";
            }
        }
        
        // Add foreign key if users table exists
        try {
            $pdo->exec("ALTER TABLE user_sessions ADD FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE");
            echo "<div class='alert alert-success'>
                    <i class='fas fa-link me-2'></i>Foreign key constraint added.
                  </div>";
        } catch (Exception $e) {
            echo "<div class='alert alert-warning'>
                    <i class='fas fa-exclamation-triangle me-2'></i>
                    Foreign key warning: " . $e->getMessage() . " (This is usually fine)
                  </div>";
        }
        
        echo "<div class='alert alert-success'>
                <i class='fas fa-check-circle me-2'></i>
                Sessions table created successfully!
              </div>";
        
        // Test basic functionality
        echo "<div class='alert alert-info'>
                <i class='fas fa-cog me-2'></i>Testing basic functionality...
              </div>";
        
        // Test insert
        $test_session_id = 'test_' . uniqid();
        $stmt = $pdo->prepare("
            INSERT INTO user_sessions (
                id, ip_address, user_agent, session_data, expires_at, device_info, location_info
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $test_data = [
            $test_session_id,
            '127.0.0.1',
            'Test User Agent',
            'test_data',
            date('Y-m-d H:i:s', time() + 3600),
            json_encode(['browser' => 'Test', 'os' => 'Test', 'device' => 'Desktop']),
            json_encode(['country' => 'Test', 'city' => 'Test'])
        ];
        
        if ($stmt->execute($test_data)) {
            echo "<div class='alert alert-success'>
                    <i class='fas fa-check-circle me-2'></i>
                    Session insert test successful!
                  </div>";
            
            // Clean up test data
            $stmt = $pdo->prepare("DELETE FROM user_sessions WHERE id = ?");
            $stmt->execute([$test_session_id]);
        }
    }
    
    // Show current session stats
    $stmt = $pdo->query("
        SELECT 
            COUNT(*) as total_sessions,
            COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_sessions
        FROM user_sessions
    ");
    $stats = $stmt->fetch();
    
    echo "<div class='row mt-4'>
            <div class='col-md-6'>
                <div class='card bg-primary text-white'>
                    <div class='card-body text-center'>
                        <h3>" . $stats['total_sessions'] . "</h3>
                        <p class='mb-0'>Total Sessions</p>
                    </div>
                </div>
            </div>
            <div class='col-md-6'>
                <div class='card bg-success text-white'>
                    <div class='card-body text-center'>
                        <h3>" . $stats['active_sessions'] . "</h3>
                        <p class='mb-0'>Active Sessions</p>
                    </div>
                </div>
            </div>
          </div>";
    
    echo "<div class='alert alert-success mt-4'>
            <h4><i class='fas fa-check-circle me-2'></i>Setup Complete!</h4>
            <p class='mb-3'>Database session management is now ready!</p>
            <h5>What's Working:</h5>
            <ul>
                <li><strong>Database Sessions:</strong> Sessions stored in database</li>
                <li><strong>Device Tracking:</strong> Browser and OS detection</li>
                <li><strong>Security:</strong> IP tracking and session management</li>
                <li><strong>Auto Cleanup:</strong> Expired sessions automatically removed</li>
            </ul>
          </div>";
    
} catch (PDOException $e) {
    echo "<div class='alert alert-danger'>
            <h4><i class='fas fa-exclamation-circle me-2'></i>Database Error</h4>
            <p><strong>Error:</strong> " . $e->getMessage() . "</p>
            <h5>Solutions:</h5>
            <ul>
                <li>Make sure XAMPP MySQL is running</li>
                <li>Check if 'eventlynx' database exists</li>
                <li>Run the main setup first to create users table</li>
                <li>Try refreshing the page</li>
            </ul>
          </div>";
}

echo "        <div class='text-center mt-4'>
                <a href='index.php' class='btn btn-primary me-2'>
                    <i class='fas fa-home me-2'></i>Homepage
                </a>
                <a href='user/sessions.php' class='btn btn-success me-2'>
                    <i class='fas fa-user me-2'></i>User Sessions
                </a>
                <a href='admin/sessions.php' class='btn btn-info'>
                    <i class='fas fa-shield-alt me-2'></i>Admin Sessions
                </a>
              </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>";
?>
