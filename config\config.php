<?php
/**
 * Main Configuration File for EventLynx
 * Contains site-wide settings and utility functions
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Site Configuration
define('SITE_NAME', 'EventLynx');
define('SITE_URL', 'http://localhost/EventLynx');
define('ADMIN_EMAIL', '<EMAIL>');
define('SITE_PHONE', '+237677069985');
define('CURRENCY_SYMBOL', 'CFA');
define('CURRENCY_CODE', 'XAF');

// File Upload Configuration
define('UPLOAD_DIR', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// Pagination
define('EVENTS_PER_PAGE', 12);
define('BOOKINGS_PER_PAGE', 10);

// Include database connection
require_once 'database.php';

/**
 * Check if user is logged in
 * @return bool
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

/**
 * Check if user is admin
 * @return bool
 */
function isAdmin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] == true;
}

/**
 * Redirect to login if not authenticated
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ' . SITE_URL . '/auth/login.php');
        exit();
    }
}

/**
 * Redirect to login if not admin
 */
function requireAdmin() {
    if (!isAdmin()) {
        header('Location: ' . SITE_URL . '/auth/login.php');
        exit();
    }
}

/**
 * Sanitize input data
 * @param string $data
 * @return string
 */
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

/**
 * Generate a random booking reference
 * @return string
 */
function generateBookingReference() {
    return 'BK' . date('Ymd') . strtoupper(substr(uniqid(), -6));
}

/**
 * Format currency
 * @param float $amount
 * @return string
 */
function formatCurrency($amount) {
    return number_format($amount, 0) . ' ' . CURRENCY_SYMBOL;
}

/**
 * Format date for display
 * @param string $date
 * @return string
 */
function formatDate($date) {
    return date('F j, Y', strtotime($date));
}

/**
 * Format time for display
 * @param string $time
 * @return string
 */
function formatTime($time) {
    return date('g:i A', strtotime($time));
}

/**
 * Get user data by ID
 * @param int $user_id
 * @return array|false
 */
function getUserById($user_id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    return $stmt->fetch();
}

/**
 * Log admin activity
 * @param int $admin_id
 * @param string $action
 * @param string $target_type
 * @param int $target_id
 * @param string $description
 */
function logAdminActivity($admin_id, $action, $target_type = null, $target_id = null, $description = null) {
    $db = getDB();
    $stmt = $db->prepare("INSERT INTO admin_logs (admin_id, action, target_type, target_id, description, ip_address) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute([$admin_id, $action, $target_type, $target_id, $description, $_SERVER['REMOTE_ADDR']]);
}
?>
